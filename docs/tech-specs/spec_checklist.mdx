---
title: Spec Checklist
description: Quick reference for milestone specification validation requirements
version: 2.1.0
status: Living
tags: [checklist, validation, reference]
---

> **📋 Complete Process Guidelines:** For comprehensive documentation processes, validation requirements, quality assurance, and all process workflows, see [Core Process Guidelines](./process/agent-rules/core.mdx#📝-documentation-process).

## 🎯 Purpose

This document provides a **quick reference** for milestone specification validation. All detailed process documentation has been consolidated into the [Core Process Guidelines](./process/agent-rules/core.mdx).

## ⚡ Quick Reference

### Required front-matter fields
- `title`, `description`, `created`, `version`, `status`, `tags`

### Required top-level headings (exact text)
1. `## 🧳 Toolchain Versions`
2. `## 🎯 Definition of Done`
3. `## 📦 Deliverables`
4. `## 🗂 Directory` or `## 🗂 Directory / API Diagram`
5. `## 🧠 Key Decisions`
6. `## ✅ Success Criteria`
7. `## 🔨 Task Breakdown`
8. `## 🤖 CI Pipeline`
9. `## 🧪 Acceptance Tests`

### Validation Commands
```bash
# Validate specification compliance
node scripts/spec-lint.mjs <spec-file>

# Agent dry-run validation
pnpm run agent:dry-run --spec <file>
```

## 🚪 Approval Gate Requirements

**Before PR Approval:**
- [ ] Spec-lint validation passes: `node scripts/spec-lint.mjs <file>`
- [ ] No missing sections or TODO placeholders
- [ ] All required headings present with exact text

**After Spec Approval:**
- [ ] Agent dry-run validation: `pnpm run agent:dry-run --spec <file>`
- [ ] All process requirements configured (see [Core Process Guidelines](./process/agent-rules/core.mdx))

---

import { Callout } from '@/components/Callout'

<Callout emoji="📋">
If any required section is intentionally empty, still include the heading and write "_N/A for this milestone_".
</Callout>
