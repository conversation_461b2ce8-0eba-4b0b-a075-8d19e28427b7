hoistPattern:
  - '*'
hoistedDependencies:
  argparse@1.0.10:
    argparse: private
  esprima@4.0.1:
    esprima: private
  extend-shallow@2.0.1:
    extend-shallow: private
  is-extendable@0.1.1:
    is-extendable: private
  js-yaml@3.14.1:
    js-yaml: private
  kind-of@6.0.3:
    kind-of: private
  section-matter@1.0.0:
    section-matter: private
  sprintf-js@1.0.3:
    sprintf-js: private
  strip-bom-string@1.0.0:
    strip-bom-string: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Mon, 26 May 2025 04:26:41 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
